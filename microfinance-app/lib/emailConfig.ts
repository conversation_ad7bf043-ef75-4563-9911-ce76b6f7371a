import nodemailer from 'nodemailer';

// Email configuration interface
export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  pool?: boolean;
  maxConnections?: number;
  maxMessages?: number;
  rateDelta?: number;
  rateLimit?: number;
}

// Email attachment interface
export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
}

// Email options interface
export interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: EmailAttachment[];
  priority?: 'high' | 'normal' | 'low';
}

// Email queue interface for better performance
interface EmailQueueItem {
  options: EmailOptions;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  retryCount: number;
}

// Global transporter instance for connection pooling
let globalTransporter: nodemailer.Transporter | null = null;
let emailQueue: EmailQueueItem[] = [];
let isProcessingQueue = false;

// Create email transporter with connection pooling
export function createEmailTransporter(): nodemailer.Transporter {
  // Return existing transporter if available
  if (globalTransporter) {
    return globalTransporter;
  }

  // Validate required environment variables
  const requiredEnvVars = [
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'SMTP_PASS'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  const config: EmailConfig = {
    host: process.env.SMTP_HOST!,
    port: parseInt(process.env.SMTP_PORT!),
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER!,
      pass: process.env.SMTP_PASS!, // App password for Gmail
    },
    // Connection pooling configuration
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    // Rate limiting (5 emails per second)
    rateDelta: 1000,
    rateLimit: 5,
  };

  // Create transporter with pooling
  globalTransporter = nodemailer.createTransporter(config);

  // Handle transporter events
  globalTransporter.on('idle', () => {
    console.log('Email transporter is idle');
  });

  globalTransporter.on('error', (error) => {
    console.error('Email transporter error:', error);
    // Reset transporter on error
    globalTransporter = null;
  });

  return globalTransporter;
}

// Close email transporter connections
export function closeEmailTransporter(): void {
  if (globalTransporter) {
    globalTransporter.close();
    globalTransporter = null;
    console.log('Email transporter connections closed');
  }
}

// Send email function with retry logic
export async function sendEmail(options: EmailOptions, maxRetries: number = 3): Promise<any> {
  return new Promise((resolve, reject) => {
    // Add to queue for processing
    emailQueue.push({
      options,
      resolve,
      reject,
      retryCount: 0
    });

    // Start processing queue if not already processing
    if (!isProcessingQueue) {
      processEmailQueue();
    }
  });
}

// Process email queue with retry logic
async function processEmailQueue(): Promise<void> {
  if (isProcessingQueue || emailQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;

  while (emailQueue.length > 0) {
    const item = emailQueue.shift()!;

    try {
      const result = await sendEmailDirect(item.options);
      item.resolve(result);
    } catch (error) {
      item.retryCount++;

      // Retry logic for transient failures
      if (item.retryCount < 3 && isRetryableError(error)) {
        console.log(`Retrying email send (attempt ${item.retryCount + 1}/3):`, error);
        // Add back to queue for retry with exponential backoff
        setTimeout(() => {
          emailQueue.unshift(item);
        }, Math.pow(2, item.retryCount) * 1000);
      } else {
        console.error('Email send failed after retries:', error);
        item.reject(error);
      }
    }

    // Small delay between emails to respect rate limits
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  isProcessingQueue = false;
}

// Direct email sending function
async function sendEmailDirect(options: EmailOptions): Promise<any> {
  const transporter = createEmailTransporter();

  // Verify transporter configuration (with timeout)
  const verifyPromise = transporter.verify();
  const timeoutPromise = new Promise((_, reject) =>
    setTimeout(() => reject(new Error('Email verification timeout')), 10000)
  );

  await Promise.race([verifyPromise, timeoutPromise]);

  // Prepare email options
  const mailOptions = {
    from: `"${process.env.SMTP_FROM_NAME || 'Microfinance App'}" <${process.env.SMTP_USER}>`,
    to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
    cc: options.cc ? (Array.isArray(options.cc) ? options.cc.join(', ') : options.cc) : undefined,
    bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc.join(', ') : options.bcc) : undefined,
    subject: options.subject,
    text: options.text,
    html: options.html,
    priority: options.priority || 'normal',
    attachments: options.attachments?.map(attachment => ({
      filename: attachment.filename,
      content: attachment.content,
      contentType: attachment.contentType || 'application/octet-stream',
    })),
  };

  // Send email with timeout
  const sendPromise = transporter.sendMail(mailOptions);
  const sendTimeoutPromise = new Promise((_, reject) =>
    setTimeout(() => reject(new Error('Email send timeout')), 30000)
  );

  const info = await Promise.race([sendPromise, sendTimeoutPromise]);
  console.log('Email sent successfully:', info.messageId);

  return info;
}

// Check if error is retryable
function isRetryableError(error: any): boolean {
  if (!error) return false;

  const retryableErrors = [
    'ECONNRESET',
    'ENOTFOUND',
    'ECONNREFUSED',
    'ETIMEDOUT',
    'timeout',
    'network',
    'connection'
  ];

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  return retryableErrors.some(retryableError =>
    errorMessage.includes(retryableError) || errorCode.includes(retryableError)
  );
}

// Test email configuration
export async function testEmailConfiguration(): Promise<boolean> {
  try {
    const transporter = createEmailTransporter();
    await transporter.verify();
    console.log('Email configuration is valid');
    return true;
  } catch (error) {
    console.error('Email configuration test failed:', error);
    return false;
  }
}

// Generate email templates
export const emailTemplates = {
  // Dashboard export email template
  dashboardExport: (recipientName: string, exportType: string, period: string) => ({
    subject: `Dashboard Export - ${exportType} Report (${period})`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Dashboard Export Report</h2>
        <p>Dear ${recipientName},</p>
        <p>Please find attached your requested dashboard export report.</p>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #374151;">Report Details:</h3>
          <ul style="color: #6b7280;">
            <li><strong>Export Type:</strong> ${exportType}</li>
            <li><strong>Period:</strong> ${period}</li>
            <li><strong>Generated On:</strong> ${new Date().toLocaleString()}</li>
          </ul>
        </div>

        <p>This report contains comprehensive financial data including:</p>
        <ul style="color: #6b7280;">
          <li>Cash inflow and outflow analysis</li>
          <li>Profit breakdown by category</li>
          <li>Transaction details</li>
          <li>Financial metrics and trends</li>
        </ul>

        <p style="margin-top: 30px;">
          Best regards,<br>
          <strong>Microfinance Management System</strong>
        </p>

        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #9ca3af;">
          This is an automated email. Please do not reply to this message.
          If you have any questions, please contact your system administrator.
        </p>
      </div>
    `,
    text: `
Dashboard Export Report

Dear ${recipientName},

Please find attached your requested dashboard export report.

Report Details:
- Export Type: ${exportType}
- Period: ${period}
- Generated On: ${new Date().toLocaleString()}

This report contains comprehensive financial data including cash inflow and outflow analysis, profit breakdown by category, transaction details, and financial metrics and trends.

Best regards,
Microfinance Management System

This is an automated email. Please do not reply to this message.
    `
  }),

  // General notification template
  notification: (recipientName: string, title: string, message: string) => ({
    subject: title,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">${title}</h2>
        <p>Dear ${recipientName},</p>
        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
          ${message}
        </div>
        <p style="margin-top: 30px;">
          Best regards,<br>
          <strong>Microfinance Management System</strong>
        </p>
      </div>
    `,
    text: `${title}\n\nDear ${recipientName},\n\n${message}\n\nBest regards,\nMicrofinance Management System`
  })
};
