// Optimized database utilities for better performance in exports and email functions
import prisma from './prisma';

// Database connection pool monitoring
export function monitorDatabaseConnections() {
  const stats = (prisma as any)._engine?.connectionPool?.stats();
  if (stats) {
    console.log('Database connection pool stats:', {
      used: stats.used,
      idle: stats.idle,
      pending: stats.pending,
      size: stats.size
    });
  }
}

// Optimized query for contributions with minimal data
export async function getOptimizedContributions(userId: number, startDate: Date, endDate: Date) {
  console.log('Fetching optimized contributions...');
  const startTime = Date.now();
  
  try {
    const contributions = await prisma.contribution.findMany({
      where: {
        chitFund: {
          createdById: userId
        },
        paidDate: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        id: true,
        amount: true,
        paidDate: true,
        month: true,
        chitFund: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        member: {
          select: {
            id: true,
            globalMember: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        paidDate: 'asc'
      }
    });
    
    const endTime = Date.now();
    console.log(`Contributions query completed in ${endTime - startTime}ms, found ${contributions.length} records`);
    
    return contributions;
  } catch (error) {
    console.error('Error fetching contributions:', error);
    throw error;
  }
}

// Optimized query for repayments with minimal data
export async function getOptimizedRepayments(userId: number, startDate: Date, endDate: Date) {
  console.log('Fetching optimized repayments...');
  const startTime = Date.now();
  
  try {
    const repayments = await prisma.repayment.findMany({
      where: {
        loan: {
          createdById: userId
        },
        paidDate: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        id: true,
        amount: true,
        paidDate: true,
        period: true,
        paymentType: true,
        loan: {
          select: {
            id: true,
            borrower: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        paidDate: 'asc'
      }
    });
    
    const endTime = Date.now();
    console.log(`Repayments query completed in ${endTime - startTime}ms, found ${repayments.length} records`);
    
    return repayments;
  } catch (error) {
    console.error('Error fetching repayments:', error);
    throw error;
  }
}

// Optimized query for auctions with minimal data
export async function getOptimizedAuctions(userId: number, startDate: Date, endDate: Date) {
  console.log('Fetching optimized auctions...');
  const startTime = Date.now();
  
  try {
    const auctions = await prisma.auction.findMany({
      where: {
        chitFund: {
          createdById: userId
        },
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        id: true,
        amount: true,
        date: true,
        month: true,
        chitFund: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        winner: {
          select: {
            id: true,
            globalMember: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'asc'
      }
    });
    
    const endTime = Date.now();
    console.log(`Auctions query completed in ${endTime - startTime}ms, found ${auctions.length} records`);
    
    return auctions;
  } catch (error) {
    console.error('Error fetching auctions:', error);
    throw error;
  }
}

// Optimized query for loans with minimal data
export async function getOptimizedLoans(userId: number, startDate: Date, endDate: Date) {
  console.log('Fetching optimized loans...');
  const startTime = Date.now();
  
  try {
    const loans = await prisma.loan.findMany({
      where: {
        createdById: userId,
        disbursementDate: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        id: true,
        amount: true,
        documentCharge: true,
        disbursementDate: true,
        borrower: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        disbursementDate: 'asc'
      }
    });
    
    const endTime = Date.now();
    console.log(`Loans query completed in ${endTime - startTime}ms, found ${loans.length} records`);
    
    return loans;
  } catch (error) {
    console.error('Error fetching loans:', error);
    throw error;
  }
}

// Batch query execution with error handling and retry logic
export async function executeBatchQueries<T>(
  queries: (() => Promise<T>)[],
  maxRetries: number = 2
): Promise<T[]> {
  console.log(`Executing ${queries.length} queries in batch...`);
  const startTime = Date.now();
  
  const results: T[] = [];
  const errors: any[] = [];
  
  for (let i = 0; i < queries.length; i++) {
    let retryCount = 0;
    let success = false;
    
    while (retryCount <= maxRetries && !success) {
      try {
        const result = await queries[i]();
        results.push(result);
        success = true;
      } catch (error) {
        retryCount++;
        console.error(`Query ${i + 1} failed (attempt ${retryCount}/${maxRetries + 1}):`, error);
        
        if (retryCount <= maxRetries) {
          // Exponential backoff
          const delay = Math.pow(2, retryCount - 1) * 1000;
          console.log(`Retrying query ${i + 1} in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          errors.push({ queryIndex: i, error });
          // Push null as placeholder to maintain array order
          results.push(null as any);
        }
      }
    }
  }
  
  const endTime = Date.now();
  console.log(`Batch queries completed in ${endTime - startTime}ms`);
  
  if (errors.length > 0) {
    console.error(`${errors.length} queries failed:`, errors);
    throw new Error(`Batch query execution failed: ${errors.length} out of ${queries.length} queries failed`);
  }
  
  return results;
}

// Memory usage monitoring utility
export function logMemoryUsage(context: string) {
  const usage = process.memoryUsage();
  console.log(`Memory usage (${context}):`, {
    heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
    external: `${Math.round(usage.external / 1024 / 1024)}MB`,
    rss: `${Math.round(usage.rss / 1024 / 1024)}MB`
  });
}

// Database health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    console.log('Database health check: OK');
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}
