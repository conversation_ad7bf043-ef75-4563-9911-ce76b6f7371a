// Performance monitoring utilities for email, export, and database operations
import { performance } from 'perf_hooks';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: any;
}

interface MemorySnapshot {
  timestamp: number;
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private memorySnapshots: MemorySnapshot[] = [];
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.EXPORT_PERFORMANCE_LOGGING === 'true';
  }

  // Start tracking a performance metric
  startMetric(name: string, metadata?: any): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata
    });

    console.log(`📊 Performance: Started tracking "${name}"`);
  }

  // End tracking a performance metric
  endMetric(name: string, additionalMetadata?: any): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ Performance: Metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;
    
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    console.log(`✅ Performance: "${name}" completed in ${duration.toFixed(2)}ms`, 
                metric.metadata ? metric.metadata : '');

    return duration;
  }

  // Take a memory snapshot
  takeMemorySnapshot(label?: string): MemorySnapshot | null {
    if (!this.isEnabled) return null;

    const usage = process.memoryUsage();
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      external: usage.external,
      rss: usage.rss
    };

    this.memorySnapshots.push(snapshot);

    if (label) {
      console.log(`🧠 Memory (${label}):`, {
        heapUsed: `${Math.round(snapshot.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(snapshot.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(snapshot.external / 1024 / 1024)}MB`,
        rss: `${Math.round(snapshot.rss / 1024 / 1024)}MB`
      });
    }

    return snapshot;
  }

  // Calculate memory usage between two snapshots
  calculateMemoryDelta(startSnapshot: MemorySnapshot, endSnapshot: MemorySnapshot): any {
    return {
      heapUsedDelta: endSnapshot.heapUsed - startSnapshot.heapUsed,
      heapTotalDelta: endSnapshot.heapTotal - startSnapshot.heapTotal,
      externalDelta: endSnapshot.external - startSnapshot.external,
      rssDelta: endSnapshot.rss - startSnapshot.rss,
      timeDelta: endSnapshot.timestamp - startSnapshot.timestamp
    };
  }

  // Log memory delta in a readable format
  logMemoryDelta(startSnapshot: MemorySnapshot, endSnapshot: MemorySnapshot, context: string): void {
    if (!this.isEnabled) return;

    const delta = this.calculateMemoryDelta(startSnapshot, endSnapshot);
    
    console.log(`📈 Memory Delta (${context}):`, {
      heapUsed: `${delta.heapUsedDelta > 0 ? '+' : ''}${Math.round(delta.heapUsedDelta / 1024 / 1024)}MB`,
      heapTotal: `${delta.heapTotalDelta > 0 ? '+' : ''}${Math.round(delta.heapTotalDelta / 1024 / 1024)}MB`,
      external: `${delta.externalDelta > 0 ? '+' : ''}${Math.round(delta.externalDelta / 1024 / 1024)}MB`,
      rss: `${delta.rssDelta > 0 ? '+' : ''}${Math.round(delta.rssDelta / 1024 / 1024)}MB`,
      duration: `${delta.timeDelta}ms`
    });
  }

  // Get all metrics
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // Get metric by name
  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name);
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.memorySnapshots = [];
    console.log('🧹 Performance: Cleared all metrics');
  }

  // Generate performance report
  generateReport(): any {
    if (!this.isEnabled) return null;

    const completedMetrics = Array.from(this.metrics.values())
      .filter(metric => metric.duration !== undefined);

    const totalDuration = completedMetrics.reduce((sum, metric) => sum + (metric.duration || 0), 0);
    const averageDuration = completedMetrics.length > 0 ? totalDuration / completedMetrics.length : 0;

    const memoryUsage = this.memorySnapshots.length > 0 ? {
      initial: this.memorySnapshots[0],
      final: this.memorySnapshots[this.memorySnapshots.length - 1],
      peak: this.memorySnapshots.reduce((max, snapshot) => 
        snapshot.heapUsed > max.heapUsed ? snapshot : max, this.memorySnapshots[0])
    } : null;

    return {
      summary: {
        totalMetrics: this.metrics.size,
        completedMetrics: completedMetrics.length,
        totalDuration: Math.round(totalDuration),
        averageDuration: Math.round(averageDuration),
        memorySnapshots: this.memorySnapshots.length
      },
      metrics: completedMetrics,
      memoryUsage,
      timestamp: new Date().toISOString()
    };
  }

  // Log performance report
  logReport(): void {
    if (!this.isEnabled) return;

    const report = this.generateReport();
    if (report) {
      console.log('📊 Performance Report:', JSON.stringify(report, null, 2));
    }
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common performance tracking patterns

// Track function execution time
export async function trackFunctionPerformance<T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: any
): Promise<T> {
  performanceMonitor.startMetric(name, metadata);
  const startSnapshot = performanceMonitor.takeMemorySnapshot();
  
  try {
    const result = await fn();
    const endSnapshot = performanceMonitor.takeMemorySnapshot();
    
    if (startSnapshot && endSnapshot) {
      performanceMonitor.logMemoryDelta(startSnapshot, endSnapshot, name);
    }
    
    performanceMonitor.endMetric(name, { success: true });
    return result;
  } catch (error) {
    const endSnapshot = performanceMonitor.takeMemorySnapshot();
    
    if (startSnapshot && endSnapshot) {
      performanceMonitor.logMemoryDelta(startSnapshot, endSnapshot, `${name} (error)`);
    }
    
    performanceMonitor.endMetric(name, { success: false, error: error instanceof Error ? error.message : 'Unknown error' });
    throw error;
  }
}

// Track database query performance
export async function trackDatabaseQuery<T>(
  queryName: string,
  query: () => Promise<T>,
  expectedRecordCount?: number
): Promise<T> {
  return trackFunctionPerformance(
    `DB Query: ${queryName}`,
    query,
    { expectedRecordCount }
  );
}

// Track email sending performance
export async function trackEmailSending<T>(
  emailType: string,
  emailFn: () => Promise<T>,
  recipientCount?: number
): Promise<T> {
  return trackFunctionPerformance(
    `Email: ${emailType}`,
    emailFn,
    { recipientCount }
  );
}

// Track Excel generation performance
export async function trackExcelGeneration<T>(
  reportType: string,
  excelFn: () => Promise<T>,
  dataSize?: number
): Promise<T> {
  return trackFunctionPerformance(
    `Excel: ${reportType}`,
    excelFn,
    { dataSize }
  );
}

// Performance monitoring middleware for API routes
export function withPerformanceMonitoring(routeName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      return trackFunctionPerformance(
        `API: ${routeName}`,
        () => method.apply(this, args)
      );
    };

    return descriptor;
  };
}

// Check if performance monitoring is enabled
export function isPerformanceMonitoringEnabled(): boolean {
  return process.env.EXPORT_PERFORMANCE_LOGGING === 'true';
}

// Log system performance summary
export function logSystemPerformance(): void {
  if (!isPerformanceMonitoringEnabled()) return;

  const usage = process.memoryUsage();
  const uptime = process.uptime();

  console.log('🖥️ System Performance:', {
    uptime: `${Math.round(uptime / 60)}m ${Math.round(uptime % 60)}s`,
    memory: {
      heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
      external: `${Math.round(usage.external / 1024 / 1024)}MB`,
      rss: `${Math.round(usage.rss / 1024 / 1024)}MB`
    },
    nodeVersion: process.version,
    platform: process.platform
  });
}
